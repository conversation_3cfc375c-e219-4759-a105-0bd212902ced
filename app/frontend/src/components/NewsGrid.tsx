import React from 'react';
import {
  Grid,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Paper,
} from '@mui/material';
import { Article } from '@mui/icons-material';
import { NewsItem } from '../types';
import NewsCard from './NewsCard';

interface NewsGridProps {
  news: NewsItem[];
  loading?: boolean;
  error?: string;
  ticker?: string;
}

const NewsGrid: React.FC<NewsGridProps> = ({
  news,
  loading = false,
  error,
  ticker,
}) => {
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          Loading news...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        <Typography variant="body1">
          Error loading news: {error}
        </Typography>
      </Alert>
    );
  }

  if (news.length === 0) {
    return (
      <Paper 
        elevation={1} 
        sx={{ 
          p: 4, 
          textAlign: 'center', 
          mt: 2,
          backgroundColor: 'grey.50'
        }}
      >
        <Article sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No news found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {ticker 
            ? `No recent news articles found for ${ticker}. Try selecting a different date range or data provider.`
            : 'Select a stock and data provider to view news articles.'
          }
        </Typography>
      </Paper>
    );
  }

  return (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
        News Articles {ticker && `for ${ticker}`} ({news.length} articles)
      </Typography>
      
      <Grid container spacing={3}>
        {news.map((newsItem, index) => (
          <Grid item xs={12} sm={6} md={4} key={`${newsItem.url}-${index}`}>
            <NewsCard news={newsItem} />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default NewsGrid;
