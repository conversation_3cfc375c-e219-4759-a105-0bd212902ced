import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
} from '@mui/material';
import StockSelector from '../components/StockSelector';
import AnalystSelector from '../components/AnalystSelector';
import ProviderSelector from '../components/ProviderSelector';
import ModelSelector from '../components/ModelSelector';
import AnalysisPanel from '../components/AnalysisPanel';
import {
  Stock,
  Analyst,
  LLMProvider,
  LLMModel,
  FormData,
  FormErrors,
  AnalysisState
} from '../types';
import {
  getAvailableAnalysts,
  getAvailableProviders,
  getAvailableModels,
  checkOllamaStatus,
  checkLMStudioStatus,
  runAnalysis
} from '../services/api';

const AnalysisPage: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    selectedStocks: [],
    selectedAnalysts: [],
    selectedProvider: '',
    selectedModel: '',
    startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    initialCash: 100000,
    marginRequirement: 0,
  });

  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [analysts, setAnalysts] = useState<Analyst[]>([]);
  const [providers, setProviders] = useState<LLMProvider[]>([]);
  const [models, setModels] = useState<LLMModel[]>([]);
  const [analysisState, setAnalysisState] = useState<AnalysisState>({
    isRunning: false,
    progress: [],
    result: null,
    error: null,
  });

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const [analystsData, providersData] = await Promise.all([
          getAvailableAnalysts(),
          getAvailableProviders(),
        ]);

        setAnalysts(analystsData);
        setProviders(providersData);

        // Auto-select first provider if available
        if (providersData.length > 0 && !formData.selectedProvider) {
          setFormData(prev => ({ ...prev, selectedProvider: providersData[0].id }));
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
      }
    };

    loadInitialData();
  }, []);

  // Load models when provider changes
  useEffect(() => {
    const loadModels = async () => {
      if (!formData.selectedProvider) {
        setModels([]);
        return;
      }

      try {
        // Check provider status for local providers
        if (formData.selectedProvider === 'Ollama') {
          const status = await checkOllamaStatus();
          if (!status.running) {
            console.warn('Ollama is not running');
          }
        } else if (formData.selectedProvider === 'LMStudio') {
          const status = await checkLMStudioStatus();
          if (!status.running) {
            console.warn('LM Studio is not running');
          }
        }

        const modelsData = await getAvailableModels(formData.selectedProvider);
        setModels(modelsData);

        // Auto-select first model if available and none selected
        if (modelsData.length > 0 && !formData.selectedModel) {
          setFormData(prev => ({ ...prev, selectedModel: modelsData[0].id }));
        } else if (modelsData.length === 0) {
          setFormData(prev => ({ ...prev, selectedModel: '' }));
        }
      } catch (error) {
        console.error('Error loading models:', error);
        setModels([]);
      }
    };

    loadModels();
  }, [formData.selectedProvider]);

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    if (formData.selectedStocks.length === 0) {
      errors.stocks = 'Please select at least one stock';
    }

    if (formData.selectedAnalysts.length === 0) {
      errors.analysts = 'Please select at least one analyst';
    }

    if (!formData.selectedProvider) {
      errors.provider = 'Please select an LLM provider';
    }

    if (!formData.selectedModel) {
      errors.model = 'Please select a model';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleStartAnalysis = async () => {
    if (!validateForm()) {
      return;
    }

    setAnalysisState({
      isRunning: true,
      progress: [],
      result: null,
      error: null,
    });

    try {
      await runAnalysis(
        {
          tickers: formData.selectedStocks.map(stock => stock.ticker),
          selectedAnalysts: formData.selectedAnalysts,
          modelProvider: formData.selectedProvider,
          modelName: formData.selectedModel,
          startDate: formData.startDate,
          endDate: formData.endDate,
          initialCash: formData.initialCash,
          marginRequirement: formData.marginRequirement,
        },
        (progress) => {
          setAnalysisState(prev => ({
            ...prev,
            progress: [...prev.progress, progress],
          }));
        },
        (result) => {
          setAnalysisState(prev => ({
            ...prev,
            isRunning: false,
            result,
          }));
        },
        (error) => {
          setAnalysisState(prev => ({
            ...prev,
            isRunning: false,
            error,
          }));
        }
      );
    } catch (error) {
      setAnalysisState(prev => ({
        ...prev,
        isRunning: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      }));
    }
  };

  const handleCancelAnalysis = () => {
    setAnalysisState(prev => ({
      ...prev,
      isRunning: false,
    }));
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Grid container spacing={3}>
        {/* Configuration Panel */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper elevation={3} sx={{ p: 3, height: 'fit-content' }}>
            <Typography variant="h5" gutterBottom>
              Analysis Configuration
            </Typography>

            <Box sx={{ mt: 3 }}>
              <StockSelector
                selectedStocks={formData.selectedStocks}
                onChange={(stocks) => setFormData(prev => ({ ...prev, selectedStocks: stocks }))}
                error={formErrors.stocks}
                maxSelections={5}
              />
            </Box>

            <Box sx={{ mt: 3 }}>
              <AnalystSelector
                analysts={analysts}
                selectedAnalysts={formData.selectedAnalysts}
                onChange={(analysts) => setFormData(prev => ({ ...prev, selectedAnalysts: analysts }))}
                error={formErrors.analysts}
              />
            </Box>

            <Box sx={{ mt: 3 }}>
              <ProviderSelector
                providers={providers}
                selectedProvider={formData.selectedProvider}
                onChange={(provider) => setFormData(prev => ({ ...prev, selectedProvider: provider }))}
                error={formErrors.provider}
              />
            </Box>

            <Box sx={{ mt: 3 }}>
              <ModelSelector
                models={models}
                selectedModel={formData.selectedModel}
                onChange={(model) => setFormData(prev => ({ ...prev, selectedModel: model }))}
                error={formErrors.model}
                provider={formData.selectedProvider}
                disabled={!formData.selectedProvider || models.length === 0}
              />
            </Box>
          </Paper>
        </Grid>

        {/* Analysis Panel */}
        <Grid size={{ xs: 12, md: 6 }}>
          <AnalysisPanel
            formData={formData}
            formErrors={formErrors}
            analysisState={analysisState}
            onStartAnalysis={handleStartAnalysis}
            onCancelAnalysis={handleCancelAnalysis}
          />
        </Grid>
      </Grid>
    </Container>
  );
};

export default AnalysisPage;
