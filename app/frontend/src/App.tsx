import React from 'react';
import {
  AppB<PERSON>,
  Too<PERSON><PERSON>,
  Typography,
  ThemeProvider,
  createTheme,
  CssBaseline,
  Box,
} from '@mui/material';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { TrendingUp } from '@mui/icons-material';
import HamburgerMenu from './components/HamburgerMenu';
import AnalysisPage from './pages/AnalysisPage';
import NewsPage from './pages/NewsPage';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    h4: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 500,
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <AppBar position="static" elevation={2}>
          <Toolbar>
            <HamburgerMenu />
            <TrendingUp sx={{ mr: 2 }} />
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              AI Hedge Fund
            </Typography>
          </Toolbar>
        </AppBar>

        <Box sx={{ minHeight: 'calc(100vh - 64px)' }}>
          <Routes>
            <Route path="/" element={<AnalysisPage />} />
            <Route path="/news" element={<NewsPage />} />
          </Routes>
        </Box>
      </Router>
    </ThemeProvider>
  );
}

export default App;
